<template>
  <div class="new-user-guide" :class="{ 'maximized': isMaximized, 'normal': !isMaximized }">
    <!-- 主容器背景 -->
    <div class="guide-background"></div>

    <!-- 进度指示器 -->
    <div class="progress-indicator">
      <div class="progress-bar">
        <div class="progress-fill" :style="{ width: progressPercentage + '%' }"></div>
      </div>
      <span class="progress-text">第{{ currentStep }}步 / 共{{ totalSteps }}步</span>
    </div>

    <!-- 内容区域 -->
    <div class="guide-content">
      <!-- 欢迎页面 -->
      <div v-if="currentStep === 1" class="step-content welcome-step">
        <div class="welcome-header">
          <h1>🎉 欢迎来到「终章」</h1>
          <p class="subtitle">你的网文创作伙伴！</p>
        </div>
        
        <div class="assistant-intro">
          <div class="assistant-avatar">🤖</div>
          <div class="assistant-message">
            <p>我是你的创作助手小章，让我来帮你开启网文创作之旅吧！</p>
          </div>
        </div>

        <div class="user-type-selection">
          <h3>请选择你的情况：</h3>
          <div class="option-cards">
            <div 
              class="option-card" 
              :class="{ active: selectedUserType === 'beginner' }"
              @click="selectUserType('beginner')"
            >
              <div class="option-icon">📚</div>
              <h4>完全新手</h4>
              <p>从来没写过小说</p>
            </div>
            <div 
              class="option-card"
              :class="{ active: selectedUserType === 'hasIdea' }"
              @click="selectUserType('hasIdea')"
            >
              <div class="option-icon">💡</div>
              <h4>有想法</h4>
              <p>有一些想法，但不知道怎么开始</p>
            </div>
            <div 
              class="option-card"
              :class="{ active: selectedUserType === 'hasExperience' }"
              @click="selectUserType('hasExperience')"
            >
              <div class="option-icon">✍️</div>
              <h4>有经验</h4>
              <p>写过一些，想要更专业的指导</p>
            </div>
          </div>
        </div>

        <div class="step-actions">
          <button 
            class="primary-btn" 
            @click="nextStep" 
            :disabled="!selectedUserType"
          >
            开始我的创作之旅
          </button>
          <button class="secondary-btn" @click="showTutorial">先看看教程</button>
        </div>
      </div>

      <!-- 创作启蒙 -->
      <div v-if="currentStep === 2" class="step-content inspiration-step">
        <div class="step-header">
          <h2>📚 新手创作第一课：什么是网文小说？</h2>
        </div>

        <div class="knowledge-cards">
          <div class="knowledge-card">
            <div class="card-icon">📖</div>
            <h4>连载形式</h4>
            <p>章节更新，保持读者期待</p>
          </div>
          <div class="knowledge-card">
            <div class="card-icon">💬</div>
            <h4>读者互动</h4>
            <p>即时反馈，调整剧情</p>
          </div>
          <div class="knowledge-card">
            <div class="card-icon">🎭</div>
            <h4>类型化创作</h4>
            <p>有固定套路，容易上手</p>
          </div>
          <div class="knowledge-card">
            <div class="card-icon">💰</div>
            <h4>商业化运作</h4>
            <p>可以赚钱，实现价值</p>
          </div>
        </div>

        <div class="practice-section">
          <h3>让我们先从一个简单的练习开始：</h3>
          <p class="practice-instruction">"用100字描述一个你感兴趣的故事想法"</p>
          
          <textarea
            v-model="storyIdea"
            class="idea-input"
            placeholder="在这里写下你的想法..."
            maxlength="200"
          ></textarea>
          
          <div class="char-count">{{ storyIdea.length }}/200字</div>

          <div class="tips-section">
            <h4>💡 提示：可以是任何你感兴趣的内容，比如：</h4>
            <ul class="tip-list">
              <li>一个普通学生突然获得超能力</li>
              <li>一个现代人穿越到古代</li>
              <li>一个游戏世界的冒险故事</li>
            </ul>
          </div>
        </div>

        <div class="step-actions">
          <button class="secondary-btn" @click="prevStep">上一步</button>
          <button 
            class="primary-btn" 
            @click="analyzeIdea" 
            :disabled="storyIdea.length < 20"
          >
            下一步：分析我的想法
          </button>
        </div>
      </div>

      <!-- 想法分析 -->
      <div v-if="currentStep === 3" class="step-content analysis-step">
        <div class="step-header">
          <h2>🔍 小章正在分析你的创作想法...</h2>
        </div>

        <div class="analysis-loading" v-if="isAnalyzing">
          <div class="loading-spinner"></div>
          <p>正在分析中，请稍候...</p>
        </div>

        <div class="analysis-result" v-if="!isAnalyzing && analysisResult">
          <div class="result-section">
            <h3>根据你的描述，我发现：</h3>
            <div class="analysis-tags">
              <span class="tag success">✅ 题材类型：{{ analysisResult.genre }}</span>
              <span class="tag success">✅ 目标读者：{{ analysisResult.audience }}</span>
              <span class="tag success">✅ 故事核心：{{ analysisResult.core }}</span>
            </div>
          </div>

          <div class="suggestions-section">
            <h3>💡 建议发展方向：</h3>
            <ol class="suggestion-list">
              <li v-for="suggestion in analysisResult.suggestions" :key="suggestion">
                {{ suggestion }}
              </li>
            </ol>
          </div>

          <div class="learning-section">
            <h3>🎯 推荐学习：</h3>
            <ul class="learning-list">
              <li v-for="topic in analysisResult.learningTopics" :key="topic">
                {{ topic }}
              </li>
            </ul>
          </div>
        </div>

        <div class="step-actions">
          <button class="secondary-btn" @click="prevStep">上一步</button>
          <button class="outline-btn" @click="showLearning">先学习相关知识</button>
          <button class="primary-btn" @click="nextStep">继续完善想法</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { getCurrentWindow } from '@tauri-apps/api/window';

// 窗口状态
const isMaximized = ref(false);

// 引导流程状态
const currentStep = ref(1);
const totalSteps = ref(8);
const selectedUserType = ref('');
const storyIdea = ref('');
const isAnalyzing = ref(false);

// 分析结果
const analysisResult = ref<{
  genre: string;
  audience: string;
  core: string;
  suggestions: string[];
  learningTopics: string[];
} | null>(null);

// 计算属性
const progressPercentage = computed(() => {
  return (currentStep.value / totalSteps.value) * 100;
});

// 方法
const selectUserType = (type: string) => {
  selectedUserType.value = type;
};

const nextStep = () => {
  if (currentStep.value < totalSteps.value) {
    currentStep.value++;
  }
};

const prevStep = () => {
  if (currentStep.value > 1) {
    currentStep.value--;
  }
};

const showTutorial = () => {
  console.log('显示教程');
};

const analyzeIdea = async () => {
  currentStep.value++;
  isAnalyzing.value = true;
  
  // 模拟分析过程
  setTimeout(() => {
    analysisResult.value = {
      genre: '都市异能',
      audience: '男频读者',
      core: '成长逆袭',
      suggestions: [
        '主角获得能力的原因和过程',
        '能力带来的机遇和挑战',
        '主角如何利用能力改变人生'
      ],
      learningTopics: [
        '都市异能类小说的经典套路',
        '男频读者喜欢的情节元素',
        '如何设计吸引人的开头'
      ]
    };
    isAnalyzing.value = false;
  }, 2000);
};

const showLearning = () => {
  console.log('显示学习内容');
};

// 检查窗口状态
const checkMaximizedState = async () => {
  try {
    const window = getCurrentWindow();
    isMaximized.value = await window.isMaximized();
  } catch (error) {
    console.error('检查窗口状态失败:', error);
  }
};

onMounted(async () => {
  await checkMaximizedState();
});
</script>

<style scoped>
.new-user-guide {
  position: absolute;
  transition: all 0.3s ease-in-out;
}

/* 最大化状态 */
.new-user-guide.maximized {
  top: 58px;
  left: 113px;
  width: 774px;
  height: 969px;
}

/* 普通状态 */
.new-user-guide.normal {
  top: 40px;
  left: 78px;
  width: 534px;
  height: 669px;
}

/* 主背景 */
.guide-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.07) 0%, rgba(255, 255, 255, 0.05) 100%);
  border: 1px solid rgba(255, 255, 255, 0.06);
  border-radius: 20px;
  backdrop-filter: blur(80px);
  -webkit-backdrop-filter: blur(80px);
}

/* 进度指示器 */
.progress-indicator {
  position: absolute;
  top: 20px;
  left: 20px;
  right: 20px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.progress-bar {
  flex: 1;
  height: 6px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #0DFF00, #00FF88);
  border-radius: 3px;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
  white-space: nowrap;
}

/* 内容区域 */
.guide-content {
  position: absolute;
  top: 60px;
  left: 20px;
  right: 20px;
  bottom: 20px;
  overflow-y: auto;
  padding: 20px;
}

.guide-content::-webkit-scrollbar {
  display: none;
}

/* 步骤内容通用样式 */
.step-content {
  color: #FFFFFF;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

/* 欢迎步骤样式 */
.welcome-header {
  text-align: center;
  margin-bottom: 30px;
}

.welcome-header h1 {
  font-size: 28px;
  margin: 0 0 10px 0;
  background: linear-gradient(45deg, #0DFF00, #00FF88);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.subtitle {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
}

.assistant-intro {
  display: flex;
  align-items: flex-start;
  gap: 15px;
  margin-bottom: 30px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.assistant-avatar {
  font-size: 24px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(13, 255, 0, 0.1);
  border-radius: 50%;
  flex-shrink: 0;
}

.assistant-message p {
  margin: 0;
  font-size: 14px;
  line-height: 1.5;
}

.user-type-selection h3 {
  margin: 0 0 20px 0;
  font-size: 16px;
}

.option-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 15px;
  margin-bottom: 30px;
}

.option-card {
  padding: 20px 15px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.option-card:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.3);
}

.option-card.active {
  background: rgba(13, 255, 0, 0.1);
  border-color: #0DFF00;
}

.option-icon {
  font-size: 24px;
  margin-bottom: 10px;
}

.option-card h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
}

.option-card p {
  margin: 0;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
}

/* 按钮样式 */
.step-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  margin-top: 30px;
}

.primary-btn, .secondary-btn, .outline-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.primary-btn {
  background: #0DFF00;
  color: #000000;
}

.primary-btn:hover:not(:disabled) {
  background: #0BCC00;
  transform: translateY(-1px);
}

.primary-btn:disabled {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.5);
  cursor: not-allowed;
}

.secondary-btn {
  background: rgba(255, 255, 255, 0.1);
  color: #FFFFFF;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.secondary-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.outline-btn {
  background: transparent;
  color: #0DFF00;
  border: 1px solid #0DFF00;
}

.outline-btn:hover {
  background: rgba(13, 255, 0, 0.1);
}

/* 创作启蒙步骤样式 */
.step-header {
  margin-bottom: 25px;
}

.step-header h2 {
  margin: 0;
  font-size: 20px;
  color: #FFFFFF;
}

.knowledge-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
  gap: 15px;
  margin-bottom: 30px;
}

.knowledge-card {
  padding: 20px 15px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  text-align: center;
  transition: all 0.2s ease;
}

.knowledge-card:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
}

.card-icon {
  font-size: 24px;
  margin-bottom: 12px;
}

.knowledge-card h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #0DFF00;
}

.knowledge-card p {
  margin: 0;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.4;
}

.practice-section {
  margin-bottom: 30px;
}

.practice-section h3 {
  margin: 0 0 15px 0;
  font-size: 16px;
}

.practice-instruction {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  margin: 0 0 15px 0;
  font-style: italic;
}

.idea-input {
  width: 100%;
  min-height: 100px;
  padding: 15px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: #FFFFFF;
  font-family: inherit;
  font-size: 14px;
  line-height: 1.5;
  resize: vertical;
  box-sizing: border-box;
}

.idea-input:focus {
  outline: none;
  border-color: #0DFF00;
  background: rgba(255, 255, 255, 0.15);
}

.idea-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.char-count {
  text-align: right;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
  margin: 8px 0 20px 0;
}

.tips-section h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #0DFF00;
}

.tip-list {
  margin: 0;
  padding-left: 20px;
  color: rgba(255, 255, 255, 0.8);
}

.tip-list li {
  font-size: 13px;
  line-height: 1.5;
  margin-bottom: 6px;
}

/* 分析步骤样式 */
.analysis-loading {
  text-align: center;
  padding: 40px 20px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.1);
  border-top: 3px solid #0DFF00;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.analysis-loading p {
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  margin: 0;
}

.analysis-result {
  animation: slideUp 0.5s ease;
}

@keyframes slideUp {
  from { opacity: 0; transform: translateY(30px); }
  to { opacity: 1; transform: translateY(0); }
}

.result-section, .suggestions-section, .learning-section {
  margin-bottom: 25px;
}

.result-section h3, .suggestions-section h3, .learning-section h3 {
  margin: 0 0 15px 0;
  font-size: 16px;
  color: #FFFFFF;
}

.analysis-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.tag {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
}

.tag.success {
  background: rgba(13, 255, 0, 0.1);
  color: #0DFF00;
  border: 1px solid rgba(13, 255, 0, 0.3);
}

.suggestion-list, .learning-list {
  margin: 0;
  padding-left: 20px;
  color: rgba(255, 255, 255, 0.8);
}

.suggestion-list li, .learning-list li {
  font-size: 13px;
  line-height: 1.6;
  margin-bottom: 8px;
}

/* 响应式调整 */
.new-user-guide.normal .welcome-header h1 {
  font-size: 24px;
}

.new-user-guide.normal .step-header h2 {
  font-size: 18px;
}

.new-user-guide.normal .knowledge-cards {
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: 12px;
}

.new-user-guide.normal .option-cards {
  grid-template-columns: repeat(auto-fit, minmax(130px, 1fr));
  gap: 12px;
}
</style>
