<template>
  <div class="writing-assistant" :class="{ 'maximized': isMaximized, 'normal': !isMaximized }">
    <!-- 主容器背景 -->
    <div class="assistant-background"></div>

    <!-- 标题栏 -->
    <div class="assistant-header">
      <div class="header-left">
        <h2>✍️ 智能写作助手</h2>
        <div class="chapter-info">
          <span class="chapter-title">{{ currentChapter }}</span>
          <span class="word-count">{{ currentWordCount }}/{{ targetWordCount }}字</span>
        </div>
      </div>
      <div class="header-right">
        <div class="writing-status">
          <div class="status-dot" :class="writingStatus"></div>
          <span class="status-text">{{ statusText }}</span>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="assistant-content">
      <!-- 左侧：大纲面板 -->
      <div class="outline-panel">
        <div class="panel-header">
          <h3>📋 当前章节大纲</h3>
        </div>
        <div class="outline-content">
          <div 
            v-for="(section, index) in chapterOutline" 
            :key="index"
            class="outline-item"
            :class="{ 
              'completed': section.status === 'completed',
              'current': section.status === 'current',
              'pending': section.status === 'pending'
            }"
          >
            <div class="outline-status">
              <span v-if="section.status === 'completed'">✓</span>
              <span v-else-if="section.status === 'current'">▶</span>
              <span v-else>○</span>
            </div>
            <div class="outline-text">{{ section.title }}</div>
          </div>
        </div>

        <div class="progress-section">
          <h4>📊 进度统计</h4>
          <div class="progress-stats">
            <div class="stat-item">
              <span class="stat-label">今日字数:</span>
              <span class="stat-value">{{ todayWordCount }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">本周字数:</span>
              <span class="stat-value">{{ weekWordCount }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">总字数:</span>
              <span class="stat-value">{{ totalWordCount }}</span>
            </div>
          </div>
        </div>

        <div class="task-section">
          <h4>🎯 当前任务</h4>
          <div class="current-task">{{ currentTask }}</div>
        </div>
      </div>

      <!-- 中间：写作区域 -->
      <div class="writing-panel">
        <div class="editor-toolbar">
          <div class="toolbar-left">
            <button class="tool-btn" @click="saveDocument" title="保存">💾</button>
            <button class="tool-btn" @click="undoAction" title="撤销">↶</button>
            <button class="tool-btn" @click="redoAction" title="重做">↷</button>
          </div>
          <div class="toolbar-right">
            <button class="tool-btn" @click="showSettings" title="设置">⚙️</button>
          </div>
        </div>

        <div class="editor-container">
          <textarea
            ref="editorTextarea"
            v-model="documentContent"
            class="content-editor"
            placeholder="开始编写您的内容..."
            @input="handleContentChange"
            @scroll="handleEditorScroll"
            @keyup="handleKeyUp"
          ></textarea>
        </div>

        <div class="editor-footer">
          <div class="footer-left">
            <span class="cursor-info">行 {{ cursorLine }}, 列 {{ cursorColumn }}</span>
          </div>
          <div class="footer-right">
            <button class="action-btn primary" @click="publishChapter">发布章节</button>
            <button class="action-btn secondary" @click="saveAsDraft">保存草稿</button>
          </div>
        </div>
      </div>

      <!-- 右侧：助手面板 -->
      <div class="helper-panel">
        <div class="panel-header">
          <h3>🤖 小章助手</h3>
        </div>

        <div class="helper-content">
          <!-- 当前建议 -->
          <div class="suggestion-section">
            <h4>💡 当前建议</h4>
            <div class="suggestion-card">
              <div class="suggestion-text">{{ currentSuggestion }}</div>
              <div class="suggestion-actions">
                <button class="mini-btn" @click="applySuggestion">采用</button>
                <button class="mini-btn" @click="nextSuggestion">下一个</button>
              </div>
            </div>
          </div>

          <!-- 读者兴趣度 */
          <div class="interest-section">
            <h4>📈 读者兴趣度</h4>
            <div class="interest-meter">
              <div class="meter-bar">
                <div class="meter-fill" :style="{ width: readerInterest + '%' }"></div>
              </div>
              <span class="meter-value">{{ readerInterest }}%</span>
            </div>
          </div>

          <!-- 注意事项 */
          <div class="warnings-section">
            <h4>⚠️ 注意事项</h4>
            <div class="warning-list">
              <div 
                v-for="warning in currentWarnings" 
                :key="warning"
                class="warning-item"
              >
                {{ warning }}
              </div>
            </div>
          </div>

          <!-- 快速操作 */
          <div class="quick-actions">
            <button class="quick-btn" @click="showExamples">查看范例</button>
            <button class="quick-btn" @click="showTips">写作技巧</button>
            <button class="quick-btn" @click="analyzeText">文本分析</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick } from 'vue';
import { getCurrentWindow } from '@tauri-apps/api/window';

// 窗口状态
const isMaximized = ref(false);

// 编辑器引用
const editorTextarea = ref<HTMLTextAreaElement>();

// 写作状态
const writingStatus = ref('active'); // active, idle, thinking
const currentChapter = ref('第1章《平凡的一天》');
const documentContent = ref('');
const targetWordCount = ref(3000);
const cursorLine = ref(1);
const cursorColumn = ref(1);

// 大纲数据
const chapterOutline = ref([
  { title: '开头场景', status: 'completed' },
  { title: '主角介绍', status: 'current' },
  { title: '日常困境', status: 'pending' },
  { title: '转折预告', status: 'pending' }
]);

// 统计数据
const todayWordCount = ref(1247);
const weekWordCount = ref(3891);
const totalWordCount = ref(3891);
const currentTask = ref('完成主角基本介绍');

// 助手数据
const currentSuggestion = ref('这段对话很生动！建议加入一些环境描写，让场景更立体。');
const readerInterest = ref(85);
const currentWarnings = ref([
  '避免信息堆砌',
  '保持节奏感'
]);

// 计算属性
const statusText = computed(() => {
  switch (writingStatus.value) {
    case 'active': return '创作中';
    case 'idle': return '暂停中';
    case 'thinking': return '思考中';
    default: return '就绪';
  }
});

const currentWordCount = computed(() => {
  return documentContent.value.replace(/\s+/g, '').length;
});

// 方法
const handleContentChange = () => {
  // 更新写作状态
  writingStatus.value = 'active';
  
  // 更新今日字数
  todayWordCount.value = currentWordCount.value;
  
  // 模拟实时建议更新
  updateSuggestions();
};

const handleEditorScroll = () => {
  // 处理编辑器滚动
};

const handleKeyUp = (event: KeyboardEvent) => {
  // 更新光标位置
  updateCursorPosition();
  
  // 检测用户是否在思考（停止输入）
  clearTimeout(thinkingTimer.value);
  thinkingTimer.value = setTimeout(() => {
    if (writingStatus.value === 'active') {
      writingStatus.value = 'thinking';
    }
  }, 3000);
};

const thinkingTimer = ref<number>();

const updateCursorPosition = () => {
  if (!editorTextarea.value) return;
  
  const textarea = editorTextarea.value;
  const text = textarea.value;
  const cursorPos = textarea.selectionStart;
  
  const textBeforeCursor = text.substring(0, cursorPos);
  const lines = textBeforeCursor.split('\n');
  
  cursorLine.value = lines.length;
  cursorColumn.value = lines[lines.length - 1].length + 1;
};

const updateSuggestions = () => {
  // 模拟根据内容更新建议
  const suggestions = [
    '这段对话很生动！建议加入一些环境描写，让场景更立体。',
    '主角的情感表达很到位，可以考虑加入一些内心独白。',
    '情节发展不错，注意保持节奏，不要过于急躁。',
    '描写很细腻，建议适当加入一些动作描写。'
  ];
  
  currentSuggestion.value = suggestions[Math.floor(Math.random() * suggestions.length)];
  
  // 更新读者兴趣度
  readerInterest.value = Math.min(100, Math.max(60, readerInterest.value + Math.random() * 10 - 5));
};

const saveDocument = () => {
  console.log('保存文档');
};

const undoAction = () => {
  console.log('撤销操作');
};

const redoAction = () => {
  console.log('重做操作');
};

const showSettings = () => {
  console.log('显示设置');
};

const publishChapter = () => {
  console.log('发布章节');
};

const saveAsDraft = () => {
  console.log('保存草稿');
};

const applySuggestion = () => {
  console.log('采用建议');
};

const nextSuggestion = () => {
  updateSuggestions();
};

const showExamples = () => {
  console.log('显示范例');
};

const showTips = () => {
  console.log('显示写作技巧');
};

const analyzeText = () => {
  console.log('分析文本');
};

// 检查窗口状态
const checkMaximizedState = async () => {
  try {
    const window = getCurrentWindow();
    isMaximized.value = await window.isMaximized();
  } catch (error) {
    console.error('检查窗口状态失败:', error);
  }
};

onMounted(async () => {
  await checkMaximizedState();
  
  // 初始化编辑器
  nextTick(() => {
    if (editorTextarea.value) {
      editorTextarea.value.focus();
    }
  });
});
</script>

<style scoped>
.writing-assistant {
  position: absolute;
  transition: all 0.3s ease-in-out;
}

/* 最大化状态 */
.writing-assistant.maximized {
  top: 58px;
  left: 113px;
  width: 1174px;
  height: 969px;
}

/* 普通状态 */
.writing-assistant.normal {
  top: 40px;
  left: 78px;
  width: 810px;
  height: 669px;
}

/* 主背景 */
.assistant-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.07) 0%, rgba(255, 255, 255, 0.05) 100%);
  border: 1px solid rgba(255, 255, 255, 0.06);
  border-radius: 20px;
  backdrop-filter: blur(80px);
  -webkit-backdrop-filter: blur(80px);
}

/* 标题栏 */
.assistant-header {
  position: absolute;
  top: 20px;
  left: 20px;
  right: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 15px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.header-left h2 {
  margin: 0 0 5px 0;
  font-size: 18px;
  color: #FFFFFF;
}

.chapter-info {
  display: flex;
  gap: 15px;
  font-size: 12px;
}

.chapter-title {
  color: #0DFF00;
  font-weight: 500;
}

.word-count {
  color: rgba(255, 255, 255, 0.7);
}

.writing-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.status-dot.active {
  background-color: #0DFF00;
  box-shadow: 0 0 6px rgba(13, 255, 0, 0.5);
}

.status-dot.idle {
  background-color: #888888;
  box-shadow: 0 0 6px rgba(136, 136, 136, 0.5);
  animation: none;
}

.status-dot.thinking {
  background-color: #FFD700;
  box-shadow: 0 0 6px rgba(255, 215, 0, 0.5);
}

@keyframes pulse {
  0% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.1); opacity: 0.7; }
  100% { transform: scale(1); opacity: 1; }
}

.status-text {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
}

/* 主要内容区域 */
.assistant-content {
  position: absolute;
  top: 80px;
  left: 20px;
  right: 20px;
  bottom: 20px;
  display: grid;
  grid-template-columns: 200px 1fr 220px;
  gap: 15px;
  color: #FFFFFF;
}

/* 左侧大纲面板 */
.outline-panel {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 12px;
  padding: 15px;
  overflow-y: auto;
}

.outline-panel::-webkit-scrollbar {
  width: 4px;
}

.outline-panel::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
}

.outline-panel::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
}

.panel-header h3 {
  margin: 0 0 15px 0;
  font-size: 14px;
  color: #0DFF00;
}

.outline-content {
  margin-bottom: 20px;
}

.outline-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.outline-item:last-child {
  border-bottom: none;
}

.outline-status {
  width: 16px;
  text-align: center;
  font-size: 12px;
}

.outline-item.completed .outline-status {
  color: #0DFF00;
}

.outline-item.current .outline-status {
  color: #FFD700;
}

.outline-item.pending .outline-status {
  color: rgba(255, 255, 255, 0.3);
}

.outline-text {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
  flex: 1;
}

.outline-item.current .outline-text {
  color: #FFFFFF;
  font-weight: 500;
}

.progress-section, .task-section {
  margin-bottom: 20px;
}

.progress-section h4, .task-section h4 {
  margin: 0 0 10px 0;
  font-size: 12px;
  color: #0DFF00;
}

.progress-stats {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  font-size: 11px;
}

.stat-label {
  color: rgba(255, 255, 255, 0.7);
}

.stat-value {
  color: #FFFFFF;
  font-weight: 500;
}

.current-task {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.8);
  padding: 8px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
  border-left: 3px solid #0DFF00;
}

/* 中间写作面板 */
.writing-panel {
  display: flex;
  flex-direction: column;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 12px;
  overflow: hidden;
}

.editor-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background: rgba(255, 255, 255, 0.05);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.toolbar-left, .toolbar-right {
  display: flex;
  gap: 8px;
}

.tool-btn {
  width: 28px;
  height: 28px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  color: #FFFFFF;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

.tool-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: #0DFF00;
}

.editor-container {
  flex: 1;
  position: relative;
}

.content-editor {
  width: 100%;
  height: 100%;
  padding: 20px;
  background: transparent;
  border: none;
  color: #FFFFFF;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.6;
  resize: none;
  outline: none;
  box-sizing: border-box;
}

.content-editor::placeholder {
  color: rgba(255, 255, 255, 0.3);
}

.editor-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background: rgba(255, 255, 255, 0.05);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.cursor-info {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.6);
}

.footer-right {
  display: flex;
  gap: 8px;
}

.action-btn {
  padding: 6px 12px;
  border: none;
  border-radius: 6px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-btn.primary {
  background: #0DFF00;
  color: #000000;
}

.action-btn.primary:hover {
  background: #0BCC00;
}

.action-btn.secondary {
  background: rgba(255, 255, 255, 0.1);
  color: #FFFFFF;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.action-btn.secondary:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* 右侧助手面板 */
.helper-panel {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 12px;
  padding: 15px;
  overflow-y: auto;
}

.helper-panel::-webkit-scrollbar {
  width: 4px;
}

.helper-panel::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
}

.helper-panel::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
}

.helper-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.suggestion-section, .interest-section, .warnings-section {
  margin-bottom: 15px;
}

.suggestion-section h4, .interest-section h4, .warnings-section h4 {
  margin: 0 0 10px 0;
  font-size: 12px;
  color: #0DFF00;
}

.suggestion-card {
  padding: 12px;
  background: rgba(13, 255, 0, 0.05);
  border: 1px solid rgba(13, 255, 0, 0.2);
  border-radius: 8px;
}

.suggestion-text {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.4;
  margin-bottom: 10px;
}

.suggestion-actions {
  display: flex;
  gap: 6px;
}

.mini-btn {
  padding: 4px 8px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  color: #FFFFFF;
  font-size: 10px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.mini-btn:hover {
  background: rgba(13, 255, 0, 0.2);
  border-color: #0DFF00;
}

.interest-meter {
  display: flex;
  align-items: center;
  gap: 10px;
}

.meter-bar {
  flex: 1;
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;
}

.meter-fill {
  height: 100%;
  background: linear-gradient(90deg, #FF4444, #FFD700, #0DFF00);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.meter-value {
  font-size: 11px;
  color: #0DFF00;
  font-weight: 500;
  min-width: 35px;
}

.warning-list {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.warning-item {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.8);
  padding: 6px 8px;
  background: rgba(255, 165, 0, 0.1);
  border: 1px solid rgba(255, 165, 0, 0.3);
  border-radius: 4px;
  border-left: 3px solid #FFA500;
}

.quick-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.quick-btn {
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  color: #FFFFFF;
  font-size: 11px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
}

.quick-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: #0DFF00;
}

/* 响应式调整 */
.writing-assistant.normal .assistant-content {
  grid-template-columns: 160px 1fr 180px;
  gap: 12px;
}

.writing-assistant.normal .header-left h2 {
  font-size: 16px;
}

.writing-assistant.normal .chapter-info {
  font-size: 11px;
}

.writing-assistant.normal .content-editor {
  font-size: 13px;
  padding: 15px;
}

.writing-assistant.normal .panel-header h3 {
  font-size: 13px;
}

.writing-assistant.normal .outline-text {
  font-size: 11px;
}

.writing-assistant.normal .stat-item {
  font-size: 10px;
}

.writing-assistant.normal .current-task {
  font-size: 10px;
}

.writing-assistant.normal .suggestion-text {
  font-size: 11px;
}

.writing-assistant.normal .warning-item {
  font-size: 10px;
}

.writing-assistant.normal .quick-btn {
  font-size: 10px;
  padding: 6px 10px;
}
</style>
