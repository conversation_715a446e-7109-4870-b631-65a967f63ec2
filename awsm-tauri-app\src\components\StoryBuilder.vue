<template>
  <div class="story-builder" :class="{ 'maximized': isMaximized, 'normal': !isMaximized }">
    <!-- 主容器背景 -->
    <div class="builder-background"></div>

    <!-- 标题栏 -->
    <div class="builder-header">
      <h2>🏗️ 故事构建向导</h2>
      <div class="step-indicator">
        <span class="current-step">第{{ currentStep }}步</span>
        <span class="step-title">{{ stepTitles[currentStep - 1] }}</span>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="builder-content">
      <!-- 主角设定 -->
      <div v-if="currentStep === 1" class="step-content character-step">
        <div class="step-intro">
          <h3>让我们一步步完善你的故事：</h3>
          <p>好的主角是故事的核心，让我们先设计你的主角</p>
        </div>

        <div class="form-section">
          <div class="form-group">
            <label>主角姓名</label>
            <input 
              v-model="characterData.name" 
              type="text" 
              placeholder="起个容易记住的名字"
              class="form-input"
            />
            <div class="input-tip">💡 起个容易记住的名字</div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label>年龄</label>
              <input 
                v-model="characterData.age" 
                type="number" 
                min="16" 
                max="30"
                class="form-input"
              />
              <div class="input-tip">💡 建议16-25岁，读者代入感强</div>
            </div>
            <div class="form-group">
              <label>职业</label>
              <input 
                v-model="characterData.occupation" 
                type="text" 
                placeholder="学生/打工族比较常见"
                class="form-input"
              />
            </div>
          </div>

          <div class="form-group">
            <label>主角性格特点（选择2-3个）</label>
            <div class="trait-options">
              <label 
                v-for="trait in personalityTraits" 
                :key="trait"
                class="trait-option"
                :class="{ active: characterData.traits.includes(trait) }"
              >
                <input 
                  type="checkbox" 
                  :value="trait"
                  v-model="characterData.traits"
                  :disabled="characterData.traits.length >= 3 && !characterData.traits.includes(trait)"
                />
                <span>{{ trait }}</span>
              </label>
            </div>
          </div>

          <div class="form-group">
            <label>主角的困境（获得能力前）</label>
            <textarea 
              v-model="characterData.background"
              placeholder="描述主角遇到的问题，比如被欺负、家庭贫困等..."
              class="form-textarea"
              rows="3"
            ></textarea>
            <div class="input-tip">💡 好的主角需要有明确的动机和成长空间</div>
          </div>
        </div>

        <div class="step-actions">
          <button class="secondary-btn" @click="goBack">返回</button>
          <button 
            class="primary-btn" 
            @click="nextStep"
            :disabled="!isCharacterDataValid"
          >
            下一步：设定能力
          </button>
        </div>
      </div>

      <!-- 能力设定 -->
      <div v-if="currentStep === 2" class="step-content power-step">
        <div class="step-intro">
          <h3>⚡ 现在让我们设计主角的超能力：</h3>
        </div>

        <div class="form-section">
          <div class="form-group">
            <label>能力类型（选择一个）</label>
            <div class="power-options">
              <label 
                v-for="power in powerTypes" 
                :key="power.type"
                class="power-option"
                :class="{ active: powerData.type === power.type }"
              >
                <input 
                  type="radio" 
                  :value="power.type"
                  v-model="powerData.type"
                />
                <div class="power-icon">{{ power.icon }}</div>
                <div class="power-info">
                  <h4>{{ power.name }}</h4>
                  <p>{{ power.description }}</p>
                </div>
              </label>
            </div>
          </div>

          <div class="form-group">
            <label>能力获得方式</label>
            <div class="origin-options">
              <label 
                v-for="origin in powerOrigins" 
                :key="origin"
                class="origin-option"
                :class="{ active: powerData.origin === origin }"
              >
                <input 
                  type="radio" 
                  :value="origin"
                  v-model="powerData.origin"
                />
                <span>{{ origin }}</span>
              </label>
            </div>
          </div>

          <div class="form-group">
            <label>能力限制（重要！）</label>
            <textarea 
              v-model="powerData.limitations"
              placeholder="描述能力的限制，比如消耗体力、有冷却时间等..."
              class="form-textarea"
              rows="3"
            ></textarea>
            <div class="tips-box">
              <h4>💡 小章提示：</h4>
              <ul>
                <li>能力太强会让故事失去悬念</li>
                <li>适当的限制让主角更有成长空间</li>
                <li>读者喜欢看主角逐渐变强的过程</li>
              </ul>
            </div>
          </div>
        </div>

        <div class="step-actions">
          <button class="secondary-btn" @click="prevStep">上一步</button>
          <button 
            class="primary-btn" 
            @click="nextStep"
            :disabled="!isPowerDataValid"
          >
            下一步：设定对手
          </button>
        </div>
      </div>

      <!-- 对手设定 -->
      <div v-if="currentStep === 3" class="step-content enemy-step">
        <div class="step-intro">
          <h3>⚔️ 好故事需要有对手和冲突：</h3>
        </div>

        <div class="form-section">
          <div class="form-group">
            <label>主要对手类型</label>
            <div class="enemy-type-options">
              <label 
                v-for="type in enemyTypes" 
                :key="type.value"
                class="enemy-type-option"
                :class="{ active: enemyData.mainType === type.value }"
              >
                <input 
                  type="radio" 
                  :value="type.value"
                  v-model="enemyData.mainType"
                />
                <div class="type-icon">{{ type.icon }}</div>
                <div class="type-info">
                  <h4>{{ type.name }}</h4>
                  <p>{{ type.description }}</p>
                </div>
              </label>
            </div>
          </div>

          <div class="enemy-stages">
            <div class="stage-section">
              <h4>🥊 初期小对手（前几章）</h4>
              <div class="form-row">
                <div class="form-group">
                  <label>姓名</label>
                  <input 
                    v-model="enemyData.early.name" 
                    type="text" 
                    placeholder="比如校霸、黑心老板"
                    class="form-input"
                  />
                </div>
                <div class="form-group">
                  <label>身份</label>
                  <input 
                    v-model="enemyData.early.identity" 
                    type="text" 
                    placeholder="身份背景"
                    class="form-input"
                  />
                </div>
              </div>
              <div class="form-group">
                <label>与主角的矛盾</label>
                <textarea 
                  v-model="enemyData.early.conflict"
                  placeholder="描述矛盾的起因..."
                  class="form-textarea"
                  rows="2"
                ></textarea>
              </div>
            </div>

            <div class="stage-section">
              <h4>🔥 中期大对手（中段剧情）</h4>
              <div class="form-row">
                <div class="form-group">
                  <label>姓名</label>
                  <input 
                    v-model="enemyData.middle.name" 
                    type="text" 
                    class="form-input"
                  />
                </div>
                <div class="form-group">
                  <label>身份</label>
                  <input 
                    v-model="enemyData.middle.identity" 
                    type="text" 
                    class="form-input"
                  />
                </div>
              </div>
              <div class="form-group">
                <label>与主角的矛盾</label>
                <textarea 
                  v-model="enemyData.middle.conflict"
                  class="form-textarea"
                  rows="2"
                ></textarea>
              </div>
            </div>

            <div class="stage-section">
              <h4>👑 终极对手（最终boss）</h4>
              <div class="form-row">
                <div class="form-group">
                  <label>姓名</label>
                  <input 
                    v-model="enemyData.final.name" 
                    type="text" 
                    class="form-input"
                  />
                </div>
                <div class="form-group">
                  <label>身份</label>
                  <input 
                    v-model="enemyData.final.identity" 
                    type="text" 
                    class="form-input"
                  />
                </div>
              </div>
              <div class="form-group">
                <label>与主角的矛盾</label>
                <textarea 
                  v-model="enemyData.final.conflict"
                  class="form-textarea"
                  rows="2"
                ></textarea>
              </div>
            </div>
          </div>

          <div class="tips-box">
            <h4>💡 小章提示：</h4>
            <ul>
              <li>对手要有合理的动机，不能为了坏而坏</li>
              <li>每个阶段的对手实力要递增</li>
              <li>最好的对手是主角的"镜像"</li>
            </ul>
          </div>
        </div>

        <div class="step-actions">
          <button class="secondary-btn" @click="prevStep">上一步</button>
          <button 
            class="primary-btn" 
            @click="generateOutline"
            :disabled="!isEnemyDataValid"
          >
            下一步：生成故事大纲
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { getCurrentWindow } from '@tauri-apps/api/window';

// 窗口状态
const isMaximized = ref(false);

// 步骤状态
const currentStep = ref(1);
const stepTitles = ['主角设定', '能力设定', '对手设定', '故事大纲'];

// 主角数据
const characterData = ref({
  name: '',
  age: 18,
  occupation: '',
  traits: [] as string[],
  background: ''
});

// 能力数据
const powerData = ref({
  type: '',
  origin: '',
  limitations: ''
});

// 对手数据
const enemyData = ref({
  mainType: '',
  early: { name: '', identity: '', conflict: '' },
  middle: { name: '', identity: '', conflict: '' },
  final: { name: '', identity: '', conflict: '' }
});

// 选项数据
const personalityTraits = [
  '热血冲动', '冷静理智', '幽默风趣', 
  '内向害羞', '正义感强', '聪明机智'
];

const powerTypes = [
  { type: 'elemental', icon: '🔥', name: '元素控制', description: '火、水、雷电等' },
  { type: 'mental', icon: '🧠', name: '精神能力', description: '读心、预知、控制等' },
  { type: 'physical', icon: '💪', name: '身体强化', description: '力量、速度、恢复等' },
  { type: 'special', icon: '👁️', name: '特殊技能', description: '透视、隐身、传送等' },
  { type: 'system', icon: '📱', name: '系统类', description: '游戏系统、签到系统等' }
];

const powerOrigins = [
  '意外事故', '神秘物品', '系统觉醒', 
  '遗传血脉', '修炼获得', '外星科技'
];

const enemyTypes = [
  { value: 'powered', icon: '⚡', name: '同样有能力的反派', description: '实力相当的对手' },
  { value: 'organization', icon: '🏢', name: '强大的组织或势力', description: '系统性的敌人' },
  { value: 'social', icon: '🌍', name: '系统性的社会问题', description: '更深层的矛盾' },
  { value: 'internal', icon: '💭', name: '主角内心的恐惧/弱点', description: '自我成长的障碍' }
];

// 计算属性
const isCharacterDataValid = computed(() => {
  return characterData.value.name.trim() && 
         characterData.value.occupation.trim() && 
         characterData.value.traits.length >= 2 && 
         characterData.value.background.trim();
});

const isPowerDataValid = computed(() => {
  return powerData.value.type && 
         powerData.value.origin && 
         powerData.value.limitations.trim();
});

const isEnemyDataValid = computed(() => {
  return enemyData.value.mainType && 
         enemyData.value.early.name.trim() && 
         enemyData.value.early.conflict.trim();
});

// 方法
const nextStep = () => {
  if (currentStep.value < stepTitles.length) {
    currentStep.value++;
  }
};

const prevStep = () => {
  if (currentStep.value > 1) {
    currentStep.value--;
  }
};

const goBack = () => {
  // 返回到新手引导
  console.log('返回新手引导');
};

const generateOutline = () => {
  console.log('生成故事大纲');
  // 这里会跳转到大纲生成页面
};

// 检查窗口状态
const checkMaximizedState = async () => {
  try {
    const window = getCurrentWindow();
    isMaximized.value = await window.isMaximized();
  } catch (error) {
    console.error('检查窗口状态失败:', error);
  }
};

onMounted(async () => {
  await checkMaximizedState();
});
</script>

<style scoped>
.story-builder {
  position: absolute;
  transition: all 0.3s ease-in-out;
}

/* 最大化状态 */
.story-builder.maximized {
  top: 58px;
  left: 113px;
  width: 774px;
  height: 969px;
}

/* 普通状态 */
.story-builder.normal {
  top: 40px;
  left: 78px;
  width: 534px;
  height: 669px;
}

/* 主背景 */
.builder-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.07) 0%, rgba(255, 255, 255, 0.05) 100%);
  border: 1px solid rgba(255, 255, 255, 0.06);
  border-radius: 20px;
  backdrop-filter: blur(80px);
  -webkit-backdrop-filter: blur(80px);
}

/* 标题栏 */
.builder-header {
  position: absolute;
  top: 20px;
  left: 20px;
  right: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 15px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.builder-header h2 {
  margin: 0;
  font-size: 20px;
  color: #FFFFFF;
}

.step-indicator {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.current-step {
  font-size: 12px;
  color: #0DFF00;
  font-weight: 500;
}

.step-title {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
}

/* 内容区域 */
.builder-content {
  position: absolute;
  top: 80px;
  left: 20px;
  right: 20px;
  bottom: 20px;
  overflow-y: auto;
  padding: 20px;
  color: #FFFFFF;
}

.builder-content::-webkit-scrollbar {
  display: none;
}

/* 步骤内容 */
.step-content {
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.step-intro {
  margin-bottom: 25px;
}

.step-intro h3 {
  margin: 0 0 10px 0;
  font-size: 18px;
  color: #FFFFFF;
}

.step-intro p {
  margin: 0;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
}

/* 表单样式 */
.form-section {
  margin-bottom: 30px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #FFFFFF;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.form-input, .form-textarea {
  width: 100%;
  padding: 10px 12px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: #FFFFFF;
  font-family: inherit;
  font-size: 13px;
  transition: all 0.2s ease;
  box-sizing: border-box;
}

.form-input:focus, .form-textarea:focus {
  outline: none;
  border-color: #0DFF00;
  background: rgba(255, 255, 255, 0.15);
}

.form-input::placeholder, .form-textarea::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.form-textarea {
  resize: vertical;
  min-height: 60px;
  line-height: 1.5;
}

.input-tip {
  font-size: 12px;
  color: #0DFF00;
  margin-top: 4px;
}

/* 性格特点选择 */
.trait-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 10px;
  margin-top: 10px;
}

.trait-option {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.trait-option:hover {
  background: rgba(255, 255, 255, 0.1);
}

.trait-option.active {
  background: rgba(13, 255, 0, 0.1);
  border-color: #0DFF00;
}

.trait-option input[type="checkbox"] {
  margin-right: 8px;
  accent-color: #0DFF00;
}

.trait-option span {
  font-size: 12px;
  color: #FFFFFF;
}

/* 能力类型选择 */
.power-options {
  display: grid;
  grid-template-columns: 1fr;
  gap: 12px;
  margin-top: 10px;
}

.power-option {
  display: flex;
  align-items: center;
  padding: 15px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.power-option:hover {
  background: rgba(255, 255, 255, 0.1);
}

.power-option.active {
  background: rgba(13, 255, 0, 0.1);
  border-color: #0DFF00;
}

.power-option input[type="radio"] {
  margin-right: 15px;
  accent-color: #0DFF00;
}

.power-icon {
  font-size: 24px;
  margin-right: 15px;
  width: 40px;
  text-align: center;
}

.power-info h4 {
  margin: 0 0 4px 0;
  font-size: 14px;
  color: #FFFFFF;
}

.power-info p {
  margin: 0;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
}

/* 能力获得方式选择 */
.origin-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 10px;
  margin-top: 10px;
}

.origin-option {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px 12px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
}

.origin-option:hover {
  background: rgba(255, 255, 255, 0.1);
}

.origin-option.active {
  background: rgba(13, 255, 0, 0.1);
  border-color: #0DFF00;
}

.origin-option input[type="radio"] {
  margin-right: 8px;
  accent-color: #0DFF00;
}

.origin-option span {
  font-size: 12px;
  color: #FFFFFF;
}

/* 对手类型选择 */
.enemy-type-options {
  display: grid;
  grid-template-columns: 1fr;
  gap: 12px;
  margin-top: 10px;
}

.enemy-type-option {
  display: flex;
  align-items: center;
  padding: 15px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.enemy-type-option:hover {
  background: rgba(255, 255, 255, 0.1);
}

.enemy-type-option.active {
  background: rgba(13, 255, 0, 0.1);
  border-color: #0DFF00;
}

.enemy-type-option input[type="radio"] {
  margin-right: 15px;
  accent-color: #0DFF00;
}

.type-icon {
  font-size: 20px;
  margin-right: 15px;
  width: 30px;
  text-align: center;
}

.type-info h4 {
  margin: 0 0 4px 0;
  font-size: 14px;
  color: #FFFFFF;
}

.type-info p {
  margin: 0;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
}

/* 对手阶段设定 */
.enemy-stages {
  margin-top: 25px;
}

.stage-section {
  margin-bottom: 25px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 10px;
}

.stage-section h4 {
  margin: 0 0 15px 0;
  font-size: 15px;
  color: #0DFF00;
}

/* 提示框 */
.tips-box {
  margin-top: 20px;
  padding: 15px;
  background: rgba(13, 255, 0, 0.05);
  border: 1px solid rgba(13, 255, 0, 0.2);
  border-radius: 8px;
}

.tips-box h4 {
  margin: 0 0 10px 0;
  font-size: 14px;
  color: #0DFF00;
}

.tips-box ul {
  margin: 0;
  padding-left: 20px;
}

.tips-box li {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.5;
  margin-bottom: 4px;
}

/* 按钮样式 */
.step-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.primary-btn, .secondary-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.primary-btn {
  background: #0DFF00;
  color: #000000;
}

.primary-btn:hover:not(:disabled) {
  background: #0BCC00;
  transform: translateY(-1px);
}

.primary-btn:disabled {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.5);
  cursor: not-allowed;
}

.secondary-btn {
  background: rgba(255, 255, 255, 0.1);
  color: #FFFFFF;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.secondary-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* 响应式调整 */
.story-builder.normal .builder-header h2 {
  font-size: 18px;
}

.story-builder.normal .step-intro h3 {
  font-size: 16px;
}

.story-builder.normal .form-row {
  grid-template-columns: 1fr;
  gap: 12px;
}

.story-builder.normal .trait-options {
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 8px;
}

.story-builder.normal .origin-options {
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 8px;
}
</style>
